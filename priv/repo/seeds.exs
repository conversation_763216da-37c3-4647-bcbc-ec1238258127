# Script for populating the database. You can run it as:
#
#     mix run priv/repo/seeds.exs
#
# Inside the script, you can read and write to any of your
# repositories directly:
#
#     PetalTest.Repo.insert!(%PetalTest.SomeSchema{})
#
# We recommend using the bang functions (`insert!`, `update!`
# and so on) as they will fail if something goes wrong.

alias PetalTest.Repo
alias PetalTest.Accounts.User

unless Repo.get_by(User, email: "<EMAIL>") do
  Repo.insert!(%User{
    email: "<EMAIL>",
    password: "TestPassword1",
    hashed_password: Bcrypt.hash_pwd_salt("TestPassword1"),
    role: :admin,
    confirmed_at: DateTime.utc_now()
  })
end

# Create 40 users
for i <- 1..40 do
  Repo.insert!(%User{
    email: "user#{i}@example.com",
    password: "TestPassword1",
    hashed_password: Bcrypt.hash_pwd_salt("TestPassword1"),
    role: :user,
    confirmed_at: DateTime.utc_now()
  })
end
