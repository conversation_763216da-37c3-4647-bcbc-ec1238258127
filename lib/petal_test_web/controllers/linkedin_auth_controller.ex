defmodule PetalTestWeb.LinkedinAuthController do
  use PetalTestWeb, :controller

  alias Assent.Strategy.Linkedin

  alias PetalTest.Accounts
  alias PetalTestWeb.UserAuth

  def config do
    Application.get_env(:petal_test, :linkedin_oauth)
  end

  def request(conn, _params) do
    config()
    |> Linkedin.authorize_url()
    |> case do
      {:ok, %{url: url, session_params: session_params}} ->
        conn = put_session(conn, :session_params, session_params)

        conn
        |> put_resp_header("location", url)
        |> send_resp(302, "")

      {:error, _error} ->
        nil
    end
  end

  def callback(conn, _params) do
    %{params: params} = fetch_query_params(conn)

    session_params = get_session(conn, :session_params)

    config()
    |> Keyword.put(:session_params, session_params)
    |> Linkedin.callback(params)
    |> case do
      {:ok, %{user: user_info, token: token}} ->
        {:ok, user} = Accounts.get_or_create_user_from_oauth(:linkedin, user_info)

        conn
        |> delete_session(:session_params)
        |> UserAuth.log_in_user(user, %{token: token})
        |> put_flash(:info, "Welcome back #{user}!")
        |> redirect(to: "/")

      {:error, _error} ->
        conn
        |> put_flash(:error, "Failed to authenticate.")
        |> redirect(to: "/")
    end
  end
end
