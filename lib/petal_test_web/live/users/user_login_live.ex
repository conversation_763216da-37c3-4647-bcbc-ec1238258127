defmodule PetalTestWeb.UserLoginLive do
  use PetalTestWeb, :live_view

  def render(assigns) do
    ~H"""
    <div class="mx-auto max-w-sm">
      <.header class="text-center">
        Log in to account
        <:subtitle>
          Don't have an account?
          <.link navigate={~p"/users/register"} class="font-semibold text-brand hover:underline">
            Sign up
          </.link>
          for an account now.
        </:subtitle>
      </.header>

      <.simple_form for={@form} id="login_form" action={~p"/users/log_in"} phx-update="ignore">
        <.field field={@form[:email]} type="email" label="Email" required />
        <.field field={@form[:password]} type="password" label="Password" required />

        <:actions>
          <.field
            wrapper_class="pb-0"
            field={@form[:remember_me]}
            type="checkbox"
            label="Keep me logged in"
          />
          <.link href={~p"/users/reset_password"} class="text-sm font-semibold">
            Forgot your password?
          </.link>
        </:actions>
        <:actions>
          <.button phx-disable-with="Logging in..." class="w-full">
            Log in <span aria-hidden="true">→</span>
          </.button>
        </:actions>
        <:actions>
          <div class="relative w-full">
            <div class="absolute inset-0 flex items-center" aria-hidden="true">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center">
              <span class="bg-white px-2 text-sm text-gray-500">Or</span>
            </div>
          </div>
        </:actions>
        <:actions>
          <.link href={~p"/auth/linkedin"} class="flex justify-center w-full">
            <img src="/images/LinkedIn-Default.png" alt="Sign in with LinkedIn" class="h-12" />
          </.link>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  def mount(_params, _session, socket) do
    email = Phoenix.Flash.get(socket.assigns.flash, :email)
    form = to_form(%{"email" => email}, as: "user")
    {:ok, assign(socket, form: form), temporary_assigns: [form: form]}
  end
end
