defmodule PetalTestWeb.UserRegistrationLive do
  use PetalTestWeb, :live_view

  require Logger

  alias PetalTest.Accounts
  alias PetalTest.Accounts.User

  alias PetalTest.Services.Recaptcha

  def render(assigns) do
    ~H"""
    <div class="mx-auto mt-20 max-w-sm">
      <.header class="text-center">
        Register for an account
        <:subtitle>
          Already registered?
          <.link navigate={~p"/users/log_in"} class="text-brand font-semibold hover:underline">
            Log in
          </.link>
          to your account now.
        </:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="registration_form"
        phx-submit="save"
        phx-change="validate"
        phx-trigger-action={@trigger_submit}
        action={~p"/users/log_in?_action=registered"}
        method="post"
        phx-hook="ReCaptcha"
      >
        <.error :if={@check_errors}>
          Oops, something went wrong! Please check the errors below.
        </.error>

        <.field field={@form[:email]} type="email" label="Email" required />
        <.field field={@form[:password]} type="password" label="Password" required />

        <input type="hidden" name="recaptcha_token" id="recaptcha_token" />

        <:actions>
          <.button phx-disable-with="Creating account..." class="w-full">
            Create an account
          </.button>
        </:actions>
        <:actions>
          <div class="relative w-full">
            <div class="absolute inset-0 flex items-center" aria-hidden="true">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center">
              <span class="bg-white px-2 text-sm text-gray-500">Or</span>
            </div>
          </div>
        </:actions>
        <:actions>
          <.link href={~p"/auth/linkedin"} class="flex w-full justify-center">
            <img src="/images/LinkedIn-Default.png" alt="Sign in with LinkedIn" class="h-12" />
          </.link>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  def mount(_params, _session, socket) do
    changeset = Accounts.change_user_registration(%User{})

    socket =
      socket
      |> assign(trigger_submit: false, check_errors: false)
      |> assign_form(changeset)

    {:ok, socket, temporary_assigns: [form: nil]}
  end

  def handle_event("save", %{"user" => user_params, "recaptcha_token" => recaptcha_token}, socket) do
    case Recaptcha.verify_token(recaptcha_token) do
      {:ok, score} when score > 0.5 ->
        Logger.info("CAPTCHA passed. Score: #{score}")

        case Accounts.register_user(user_params) do
          {:ok, user} ->
            {:ok, _} =
              Accounts.deliver_user_confirmation_instructions(
                user,
                &url(~p"/users/confirm/#{&1}")
              )

            changeset = Accounts.change_user_registration(user)
            {:noreply, socket |> assign(trigger_submit: true) |> assign_form(changeset)}

          {:error, %Ecto.Changeset{} = changeset} ->
            {:noreply,
             socket |> assign(check_errors: true) |> assign_form(fix_email_validation(changeset))}
        end

      {:ok, score} ->
        Logger.info("Suspicious activity detected. Score: #{score}")
        {:noreply, put_flash(socket, :error, "Suspicious activity detected. Score: #{score}")}

      {:error, reason} ->
        Logger.info("CAPTCHA failed: #{reason}")
        {:noreply, put_flash(socket, :error, "CAPTCHA failed: #{reason}")}
    end
  end

  def handle_event("validate", %{"user" => user_params}, socket) do
    changeset = Accounts.change_user_registration(%User{}, user_params)

    {:noreply, assign_form(socket, Map.put(changeset, :action, :validate))}
  end

  defp assign_form(socket, %Ecto.Changeset{} = changeset) do
    form = to_form(changeset, as: "user")

    if changeset.valid? do
      assign(socket, form: form, check_errors: false)
    else
      assign(socket, form: form)
    end
  end

  defp fix_email_validation(changeset) do
    %{
      changeset
      | errors:
          Enum.map(changeset.errors, fn
            {:email, {"has already been taken" = msg, _meta}} ->
              {:email, {msg, [validation: :unsafe_unique]}}

            other ->
              other
          end)
    }
  end
end
