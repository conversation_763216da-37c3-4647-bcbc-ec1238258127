<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content={Plug.CSRFProtection.get_csrf_token()} />
    <.live_title default="PetalTest" suffix=" · Petal Test">
      {assigns[:page_title]}
    </.live_title>
    <link phx-track-static rel="stylesheet" href={~p"/assets/app.css"} />
    <!-- Alpine Plugins -->
    <script defer src="https://cdn.jsdelivr.net/npm/@alpinejs/persist@3.x.x/dist/cdn.min.js">
    </script>
    <!-- Alpine Core -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js">
    </script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js">
    </script>
    <script defer src="https://unpkg.com/js-cookie@3.x.x/dist/js.cookie.min.js">
    </script>
    <script defer phx-track-static type="text/javascript" src={~p"/assets/app.js"}>
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://www.google.com" />
    <link rel="preconnect" href="https://www.gstatic.com" crossorigin />
    <script src="https://www.google.com/recaptcha/api.js?render=BLAH">
    </script>

    <link
      href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap"
      rel="stylesheet"
    />
    <.color_scheme_switch_js />
  </head>
  <body class="min-h-screen overflow-x-hidden bg-white">
    {@inner_content}
  </body>
</html>
