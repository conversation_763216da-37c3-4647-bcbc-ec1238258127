defmodule PetalTestWeb.AdminAuth do
  use PetalTestWeb, :verified_routes

  # import Plug.Conn
  # import Phoenix.Controller

  # alias PetalTest.Accounts

  def on_mount(:ensure_admin, _params, _session, socket) do
    if socket.assigns.current_user.role == :admin do
      {:cont, socket}
    else
      socket =
        socket
        |> Phoenix.LiveView.put_flash(:error, "You must be an admin to access this page.")
        |> Phoenix.LiveView.redirect(to: ~p"/")

      {:halt, socket}
    end
  end
end
