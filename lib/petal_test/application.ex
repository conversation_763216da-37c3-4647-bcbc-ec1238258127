defmodule PetalTest.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      PetalTestWeb.Telemetry,
      PetalTest.Repo,
      {DNSCluster, query: Application.get_env(:petal_test, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: PetalTest.PubSub},
      # Start the Finch HTTP client for sending emails
      {<PERSON>, name: PetalTest.Finch},
      # Start a worker by calling: PetalTest.Worker.start_link(arg)
      # {PetalTest.Worker, arg},
      # Start to serve requests, typically the last entry
      PetalTestWeb.Endpoint
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: PetalTest.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    PetalTestWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
