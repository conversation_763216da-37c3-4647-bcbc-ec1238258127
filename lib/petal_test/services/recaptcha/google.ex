defmodule PetalTest.Services.Recaptcha.Google do
  @moduledoc """
  Recaptcha module
  """
  @behaviour PetalTest.Services.Recaptcha.Behaviour

  def verify_token(token) do
    body =
      URI.encode_query(%{
        secret: secret_key(),
        response: token
      })

    headers = [
      {"Content-Type", "application/x-www-form-urlencoded"}
    ]

    request = Finch.build(:post, "https://www.google.com/recaptcha/api/siteverify", headers, body)

    case Finch.request(request, PetalTest.Finch) do
      {:ok, %Finch.Response{status: 200, body: response_body}} ->
        case Jason.decode(response_body) do
          {:ok, %{"success" => true, "score" => score}} ->
            {:ok, score}

          {:ok, %{"success" => false, "error-codes" => errors}} ->
            {:error, Enum.join(errors, ", ")}

          {:error, decode_error} ->
            {:error, "Decode error: #{inspect(decode_error)}"}

          _ ->
            {:error, "Unexpected JSON response"}
        end

      {:ok, %<PERSON>.Response{status: status}} ->
        {:error, "Non-200 status: #{status}"}

      {:error, error} ->
        {:error, "Request failed: #{inspect(error)}"}
    end
  end

  defp secret_key do
    Application.get_env(:petal_test, PetalTest.Services.Recaptcha.Google)[:secret_key]
  end
end
