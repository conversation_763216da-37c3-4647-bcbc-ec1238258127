defmodule PetalTest.Services.Recaptcha do
  @moduledoc """
  Recaptcha service
  """

  @callback verify_token(token :: String.t()) :: {:ok, float()} | {:error, String.t()}
  @doc """
  Verify a recaptcha token with the configured implementation, returns a tuple with the score or an error
  """
  def verify_token(token), do: impl().verify_token(token)

  defp impl do
    Application.get_env(:petal_test, :recaptcha)
  end
end
