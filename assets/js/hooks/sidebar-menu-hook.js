/**
 * SidebarMenuHook - Phoenix LiveView hook to replace Alpine.js functionality in the sidebar menu
 * 
 * This hook handles:
 * 1. Dropdown menu open/close state
 * 2. Responding to sidebar collapsed state changes
 */

const SidebarMenuHook = {
  mounted() {
    // Initialize state
    this.initState();
    
    // Set up event listeners
    this.setupEventListeners();
    
    // Apply initial state
    this.applyState();
    
    // Listen for sidebar collapsed state changes
    this.handleSidebarCollapsedChange = this.handleSidebarCollapsedChange.bind(this);
    window.addEventListener('sidebar-collapsed-changed', this.handleSidebarCollapsedChange);
  },

  destroyed() {
    // Clean up event listeners
    window.removeEventListener('sidebar-collapsed-changed', this.handleSidebarCollapsedChange);
  },

  initState() {
    // Get initial state from data attributes
    const isActive = this.el.dataset.active === 'true';

    // Initialize state
    this.state = {
      open: isActive,
      isCollapsed: false // Will be updated by sidebar-collapsed-changed event
    };

    // Find elements
    this.toggleButton = this.el.querySelector('[data-dropdown-toggle]');
    this.dropdownContent = this.el.querySelector('[data-dropdown-content]');
    this.separator = this.el.querySelector('[data-dropdown-separator]');
  },

  setupEventListeners() {
    if (this.toggleButton) {
      this.toggleButton.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleDropdown();
      });
    }
  },

  applyState() {
    if (!this.dropdownContent) return;
    
    if (this.state.open) {
      this.dropdownContent.classList.remove('hidden');
      if (this.separator && this.state.isCollapsed) {
        this.separator.classList.remove('hidden');
      }
    } else {
      this.dropdownContent.classList.add('hidden');
      if (this.separator) {
        this.separator.classList.add('hidden');
      }
    }
    
    // Update toggle button icon rotation
    const chevron = this.toggleButton?.querySelector('[data-dropdown-chevron]');
    if (chevron) {
      if (this.state.open) {
        chevron.classList.add('rotate-90');
      } else {
        chevron.classList.remove('rotate-90');
      }
    }
  },

  toggleDropdown() {
    this.state.open = !this.state.open;
    this.applyState();
  },

  handleSidebarCollapsedChange(event) {
    this.state.isCollapsed = event.detail.isCollapsed;
    this.applyState();
    
    // Update classes based on collapsed state
    if (this.toggleButton) {
      if (this.state.isCollapsed) {
        this.toggleButton.classList.add('w-min', 'gap-0');
        this.toggleButton.classList.remove('w-full', 'gap-3');
      } else {
        this.toggleButton.classList.remove('w-min', 'gap-0');
        this.toggleButton.classList.add('w-full', 'gap-3');
      }
    }
    
    // Update tippy tooltip
    if (this.toggleButton && this.toggleButton._tippy) {
      if (this.state.isCollapsed) {
        this.toggleButton._tippy.enable();
      } else {
        this.toggleButton._tippy.disable();
      }
    }
  }
};

export default SidebarMenuHook;
