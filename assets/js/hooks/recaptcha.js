
const ReCaptcha = {
    mounted() {
        this.el.addEventListener("submit", async (event) => {
            if (!this.shouldSubmit()) {
                event.stopPropagation()
                event.preventDefault()

                token = await grecaptcha.execute('BLAH', { action: 'submit' });

                const recaptchaInput = event.target.querySelector('#recaptcha_token');
                if (recaptchaInput) {
                    recaptchaInput.value = token;
                }

                this.submit_flag = true;
                this.el.dispatchEvent(new Event("submit", { bubbles: true, cancelable: true }))
            }
        });
    },
    shouldSubmit() {
        console.log("Checking if we should submit the form", this.submit_flag);
        return this.submit_flag;
    },
    submit_flag: false,
}

export default ReCaptcha;