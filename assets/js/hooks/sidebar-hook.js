/**
 * SidebarHook - Phoenix LiveView hook to replace Alpine.js functionality in the sidebar
 * 
 * This hook handles:
 * 1. Sidebar open/close state (mobile responsive)
 * 2. Sidebar collapsed/expanded state (with persistence)
 * 3. Keyboard navigation (Escape to close)
 * 4. Click-away behavior
 */

const SidebarHook = {
  mounted() {
    // Initialize state
    this.initState();
    
    // Set up event listeners
    this.setupEventListeners();
    
    // Apply initial state
    this.applyState();
    
    // Handle window resize events
    this.handleResize = this.handleResize.bind(this);
    window.addEventListener('resize', this.handleResize);
  },

  destroyed() {
    // Clean up event listeners
    window.removeEventListener('resize', this.handleResize);
    document.removeEventListener('keydown', this.handleKeyDown);
    document.removeEventListener('click', this.handleClickAway);
  },

  initState() {
    // Get configuration from data attributes
    this.isCollapsible = this.el.dataset.isCollapsible === 'true';
    this.collapsedOnly = this.el.dataset.collapsedOnly === 'true';
    this.defaultCollapsed = this.el.dataset.defaultCollapsed === 'true';
    this.sidebarWidthClass = this.el.dataset.sidebarWidthClass || 'w-64';
    this.sidebarLgWidthClass = this.el.dataset.sidebarLgWidthClass || 'lg:w-64';

    // Initialize state
    this.state = {
      sidebarOpen: false,
      isCollapsed: this.getInitialCollapsedState()
    };

    // Find sidebar elements
    this.sidebar = this.el.querySelector('#sidebar');
    this.collapseButton = this.el.querySelector('[data-collapse-sidebar]');
    this.mobileOpenButton = this.el.querySelector('[data-open-sidebar]');
    this.mobileCloseButton = this.el.querySelector('[data-close-sidebar]');
    this.sidebarOverlay = this.el.querySelector('[data-sidebar-overlay]');
  },

  getInitialCollapsedState() {
    if (this.collapsedOnly) {
      return true;
    }
    
    if (this.isCollapsible) {
      // Check localStorage for persisted state
      const persistedState = localStorage.getItem('sidebar-collapsed');
      if (persistedState !== null) {
        return persistedState === 'true';
      }
      // Use default if no persisted state
      return this.defaultCollapsed;
    }
    
    return false;
  },

  setupEventListeners() {
    // Collapse/expand button
    if (this.collapseButton && this.isCollapsible && !this.collapsedOnly) {
      this.collapseButton.addEventListener('click', () => this.toggleCollapsed());
    }
    
    // Mobile open button
    if (this.mobileOpenButton) {
      this.mobileOpenButton.addEventListener('click', () => this.toggleSidebar());
    }
    
    // Mobile close button
    if (this.mobileCloseButton) {
      this.mobileCloseButton.addEventListener('click', () => this.toggleSidebar());
    }
    
    // Escape key to close sidebar
    this.handleKeyDown = (e) => {
      if (e.key === 'Escape' && this.state.sidebarOpen) {
        this.closeSidebar();
      }
    };
    document.addEventListener('keydown', this.handleKeyDown);
    
    // Click away to close sidebar
    this.handleClickAway = (e) => {
      if (this.state.sidebarOpen && this.sidebar && !this.sidebar.contains(e.target) && 
          this.mobileOpenButton && !this.mobileOpenButton.contains(e.target)) {
        this.closeSidebar();
      }
    };
    document.addEventListener('click', this.handleClickAway);
  },

  applyState() {
    // Apply collapsed state
    this.applyCollapsedState();
    
    // Apply sidebar open/closed state
    this.applySidebarOpenState();
  },

  applyCollapsedState() {
    const isCollapsed = this.state.isCollapsed;

    // Update container classes
    const container = this.el.querySelector('[data-sidebar-container]');
    if (container) {
      if (isCollapsed) {
        container.classList.add('w-min', 'lg:w-min');
        container.classList.remove(this.sidebarLgWidthClass);
      } else {
        container.classList.remove('w-min', 'lg:w-min');
        container.classList.add(this.sidebarLgWidthClass);
      }
    }

    // Update sidebar classes
    if (this.sidebar) {
      if (isCollapsed) {
        this.sidebar.classList.add('w-min');
      } else {
        this.sidebar.classList.remove('w-min');
      }
    }

    // Update all elements with data-collapsed-toggle attribute (show when expanded, hide when collapsed)
    document.querySelectorAll('[data-collapsed-toggle]').forEach(el => {
      if (isCollapsed) {
        el.classList.add('hidden');
      } else {
        el.classList.remove('hidden');
      }
    });

    // Update all elements with data-collapsed-only attribute (show when collapsed, hide when expanded)
    document.querySelectorAll('[data-collapsed-only]').forEach(el => {
      if (isCollapsed) {
        el.classList.remove('hidden');
      } else {
        el.classList.add('hidden');
      }
    });

    // Update all elements with data-collapsed-class attribute
    document.querySelectorAll('[data-collapsed-class]').forEach(el => {
      const collapsedClass = el.dataset.collapsedClass;
      const expandedClass = el.dataset.expandedClass || '';

      if (isCollapsed) {
        expandedClass.split(' ').filter(c => c).forEach(c => el.classList.remove(c));
        collapsedClass.split(' ').filter(c => c).forEach(c => el.classList.add(c));
      } else {
        collapsedClass.split(' ').filter(c => c).forEach(c => el.classList.remove(c));
        expandedClass.split(' ').filter(c => c).forEach(c => el.classList.add(c));
      }
    });

    // Enable/disable tooltips based on collapsed state
    document.querySelectorAll('[data-tippy-content]').forEach(el => {
      if (el._tippy) {
        if (isCollapsed) {
          el._tippy.enable();
        } else {
          el._tippy.disable();
        }
      }
    });
  },

  applySidebarOpenState() {
    if (!this.sidebar || !this.sidebarOverlay) return;
    
    if (this.state.sidebarOpen) {
      // Show sidebar and overlay
      this.sidebar.classList.remove('-translate-x-full');
      this.sidebar.classList.add('translate-x-0');
      this.sidebarOverlay.classList.remove('hidden');
      
      // Set aria-expanded
      if (this.mobileOpenButton) {
        this.mobileOpenButton.setAttribute('aria-expanded', 'true');
      }
    } else {
      // Hide sidebar and overlay
      this.sidebar.classList.add('-translate-x-full');
      this.sidebar.classList.remove('translate-x-0');
      this.sidebarOverlay.classList.add('hidden');
      
      // Set aria-expanded
      if (this.mobileOpenButton) {
        this.mobileOpenButton.setAttribute('aria-expanded', 'false');
      }
    }
  },

  toggleCollapsed() {
    this.state.isCollapsed = !this.state.isCollapsed;
    
    // Persist state to localStorage
    localStorage.setItem('sidebar-collapsed', this.state.isCollapsed);
    
    // Apply new state
    this.applyCollapsedState();
    
    // Dispatch event for other components to react
    window.dispatchEvent(new CustomEvent('sidebar-collapsed-changed', {
      detail: { isCollapsed: this.state.isCollapsed }
    }));
  },

  toggleSidebar() {
    this.state.sidebarOpen = !this.state.sidebarOpen;
    this.applySidebarOpenState();
  },

  closeSidebar() {
    this.state.sidebarOpen = false;
    this.applySidebarOpenState();
  },

  handleResize() {
    // Close sidebar on larger screens
    if (window.innerWidth >= 1024 && this.state.sidebarOpen) {
      this.closeSidebar();
    }
  }
};

export default SidebarHook;
