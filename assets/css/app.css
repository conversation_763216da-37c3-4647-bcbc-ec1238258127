@import "tailwindcss";

@source "../../deps/petal_components/**/*.*ex";
@import "../../deps/petal_components/assets/default.css";

@import "./colors.css";

@plugin "@tailwindcss/typography";
@plugin "@tailwindcss/forms";
@plugin "../js/tailwind_heroicons.js";

@custom-variant dark (&:where(.dark, .dark *));

@variant phx-click-loading ([".phx-click-loading&", ".phx-click-loading &"]);
@variant phx-submit-loading ([".phx-submit-loading&", ".phx-submit-loading &"]);
@variant phx-change-loading ([".phx-change-loading&", ".phx-change-loading &"]);

@layer base {
  /* Add base styles here (eg. styles that are the defaults for common elements)

    Example base style:
    h1 {
      @apply text-2xl;
    }

  */

  /* Use the pointer for buttons */
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}

@layer components {
  /* Add component styles here (eg. buttons or tabs or anything that uses a number of styles)

    Example change button styles:

    .pc-button {
      @apply inline-flex items-center justify-center font-semibold tracking-wider uppercase transition duration-150 ease-in-out border-2 rounded-none focus:outline-hidden;
    }
    .pc-button--primary {
      @apply text-black border-black bg-primary-400 hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-800 focus:shadow-primary-500/50;
    }
  */

}

/* LiveView specific classes for your customization */
@utility phx-click-loading {
  opacity: 0.5;
  transition: opacity 1s ease-out;
}

@utility phx-loading {
  cursor: wait;
}

@layer theme {
  :root {
    --font-sans: "Plus Jakarta Sans", sans-serif;
    --font-serif: "Georgia", serif;
    --font-mono: "Menlo", monospace;
    /* Brand colors */
    --color-skyblue-light: #00E6FF;
    --color-skyblue: #00AFD6;
    --color-skyblue-dark: #0084AC;
  }

}

@layer utilities {
  .border-skyblue {
    border-color: var(--color-skyblue);
  }

  .border-skyblue-light {
    border-color: var(--color-skyblue-light);
  }

  .border-skyblue-dark {
    border-color: var(--color-skyblue-dark);
  }

  .word-spacing-tight {
    word-spacing: -0.1em;
  }

  .word-spacing-normal {
    word-spacing: normal;
  }

  .word-spacing-wide {
    word-spacing: 0.125em;
  }

  .word-spacing-wider {
    word-spacing: 0.25em;
  }

  .word-spacing-widest {
    word-spacing: 0.5em;
  }

  .word-spacing-h1 {
    word-spacing: 5px;
  }

  .bg-skyblue {
    background-color: var(--color-skyblue);
  }

  .bg-skyblue-dark {
    background-color: var(--color-skyblue-dark);
  }

  .bg-skyblue-light {
    background-color: var(--color-skyblue-light);
  }

  .text-skyblue {
    color: var(--color-skyblue);
  }

  .text-skyblue-dark {
    color: var(--color-skyblue-dark);
  }

  .text-skyblue-light {
    color: var(--color-skyblue-light);
  }
}