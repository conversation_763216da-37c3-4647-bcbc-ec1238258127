import Config

# Only in tests, remove the complexity from the password hashing algorithm
config :bcrypt_elixir, :log_rounds, 1

config :petal_test, :recaptcha, PetalTest.Services.Recaptcha.Mock
# Configure your database
#
# The MIX_TEST_PARTITION environment variable can be used
# to provide built-in test partitioning in CI environment.
# Run `mix help test` for more information.
config :petal_test, PetalTest.Repo,
  username: "postgres",
  password: "postgres",
  hostname: "localhost",
  database: "petal_test_test#{System.get_env("MIX_TEST_PARTITION")}",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: System.schedulers_online() * 2

# We don't run a server during test. If one is required,
# you can enable the server option below.
config :petal_test, PetalTestWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4002],
  secret_key_base: "VaZlqUIajQOYjwZqLoiAm0KW/uMgHDgl+C91nyZwE7pSFRO7xs2crrIzGJZbqWns",
  server: false

# In test we don't send emails
config :petal_test, PetalTest.Mailer, adapter: Swoosh.Adapters.Test

# Disable swoosh api client as it is only required for production adapters
config :swoosh, :api_client, false

# Print only warnings and errors during test
config :logger, level: String.to_atom(System.get_env("LOG_LEVEL", "warning"))

# Initialize plugs at runtime for faster test compilation
config :phoenix, :plug_init_mode, :runtime

# Enable helpful, but potentially expensive runtime checks
config :phoenix_live_view,
  enable_expensive_runtime_checks: true
