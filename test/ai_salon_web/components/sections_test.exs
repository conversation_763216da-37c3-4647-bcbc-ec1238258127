defmodule PetalTestWeb.Components.SectionsTest do
  use PetalTestWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  import Phoenix.Component

  import PetalTestWeb.Components.Sections

  test "renders the section component" do
    assert render_component(fn assigns ->
             ~H"""
             <.section>
               <div>Test</div>
             </.section>
             """
           end)
  end

  test "renders the header section component" do
    assert render_component(fn assigns ->
             ~H"""
             <.header_section>
               <div>Test</div>
             </.header_section>
             """
           end)
  end

  test "renders the container section" do
    assert render_component(fn assigns ->
             ~H"""
             <.container>
               <div>Test</div>
             </.container>
             """
           end)
  end
end
