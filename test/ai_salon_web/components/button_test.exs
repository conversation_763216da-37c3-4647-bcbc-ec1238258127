defmodule PetalTestWeb.Components.ButtonTest do
  use PetalTestWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  import Phoenix.Component

  import PetalTestWeb.Components.Button

  test "renders the component" do
    assert render_component(fn assigns -> ~H"<.button>Test</.button>" end)
  end

  test "renders the inner slot" do
    assert render_component(fn assigns -> ~H"<.button>Test</.button>" end) =~
             "Test"
  end

  test "renders the class" do
    assert render_component(fn assigns ->
             ~H"""
             <.button class="test-class">Test</.button>
             """
           end) =~ "test-class"
  end
end
